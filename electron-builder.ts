// This file needs to be closed during the build process, otherwise it will cause symlink error.

const now = new Date();
const year = now.getFullYear();
const month = String(now.getMonth() + 1);
const day = now.getDate();
const hours = now.getHours(); // No leading zero
const minutes = String(now.getMinutes()).padStart(2, '0');

const newVersion = `${year}.${month}.${day}.${hours}${minutes}`;

const CI = Boolean(process.env.CI);

const artifactName = CI ? '${productName}.${ext}' : '${productName}-${buildVersion}.${ext}';

// CI will fallback to package.json version which is updated via TAG version
const buildVersion = CI ? null : newVersion;

const builderConfig = {
	buildVersion,
	artifactName,
	files: ['build/**/*'],
	extraFiles: [{ from: './.env', to: './.env.example' }],
	appId: 'ee.platvorm.${name}',
	// nodeGypRebuild: false,
	// buildDependenciesFromSource: false,
	npmRebuild: false,
	win: {
		target: CI ? 'zip' : 'dir',
		asar: true,
	},
};

export default builderConfig;
