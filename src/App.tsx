import styled from 'styled-components';
import { LocalStyles } from './styles/LocalStyles.ts';
import { QuizContextProvider } from './contexts/QuizContext.tsx';
import { Quiz } from './components/Quiz.tsx';

import './styles/fonts/fonts.css';
import { GlobalStyles } from '@swedbank/components';
import { GameState, useGameContext } from './contexts/GameContext.tsx';
import { useRootContext } from './contexts/RootContext.tsx';
import { Intro } from './components/Intro.tsx';
import { Outro } from '@/components/Outro.tsx';
import { Standby } from '@/components/views/Standby.tsx';
import { useKeyEvents } from '@/hooks/useKeyEvents.ts';
import { useTransmit } from '@/hooks/useTransmit.ts';

const Container = styled.div`
	flex: 1;
	display: flex;
	flex-direction: column;
	padding: 60px 60px 146px;
`;

function App() {
	const { gameData } = useRootContext();
	const { startStandby, startGame, startIntro, gameState } = useGameContext();

	useTransmit();

	useKeyEvents({
		r: startStandby,
	});

	return (
		<>
			<GlobalStyles />
			<LocalStyles />
			<Container>
				{gameState === GameState.Standby ? (
					<Standby onExit={startIntro} />
				) : (
					<>
						{gameState === GameState.Intro ? (
							<Intro introTimeLimit={gameData.config.introTimeLimit} onExit={startGame} />
						) : (
							<>
								<QuizContextProvider>
									{gameState === GameState.Started && <Quiz />}
									{gameState === GameState.Outro && <Outro />}
								</QuizContextProvider>
							</>
						)}
					</>
				)}
			</Container>
		</>
	);
}

export default App;
