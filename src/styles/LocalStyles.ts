import { createGlobalStyle } from 'styled-components';

export const LocalStyles = createGlobalStyle`
    :root {
        --color-neutral: var(--pineapple);
		--color-positive: var(--alert-green); 
		--color-negative: var(--alert-red); 
    }

    body {
        margin: 0;
        padding: 0;
        font-family: Roboto, sans-serif;
        line-height: 1.2;

        overflow: hidden;

        background: var(--background);
        color: var(--text);

        display: flex;
	    flex-direction: column;
        height: 100vh;

        & > div {
            flex: 1;
            display: flex;
	        flex-direction: column;
        }
    }

    * {
        box-sizing: border-box;
    }
`;
