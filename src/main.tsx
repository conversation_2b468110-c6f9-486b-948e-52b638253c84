import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import { loadConfig } from '@/utils/config-loader.ts';
import { RootContextProvider } from './contexts/RootContext.tsx';
import { LocaleContextProvider } from '@platvorm/i18n';
import { getAssetPath } from '@/utils/assets.ts';
import { fetchWithParser } from './utils/data-loader.ts';
import { GameContextProvider } from './contexts/GameContext.tsx';
import { TransmitProvider } from '@swedbank/transmit';
import { Env } from '@/utils/env.ts';

(async () => {
	const isApp = !!window.api?.electron();
	const config = await loadConfig<ConfigRecord>({ isApp });
	console.log('Config: ', config);
	const quizData: GameData = await fetchWithParser(getAssetPath('data.yml'));
	const apiHost: string = Env.get('APP_API_BASE_URL')!;

	ReactDOM.createRoot(document.getElementById('root')!).render(
		<React.StrictMode>
			<RootContextProvider config={config} gameData={quizData}>
				<LocaleContextProvider
					defaultLocale={config.i18n?.defaultLocale || 'et'}
					locales={config.i18n?.locales || ['et']}
					assetPath={getAssetPath('locales')}
					mdKeys={['text', 'A', 'B', 'C', 'D']}
				>
					<TransmitProvider baseUrl={apiHost}>
						<GameContextProvider>
							<App />
						</GameContextProvider>
					</TransmitProvider>
				</LocaleContextProvider>
			</RootContextProvider>
		</React.StrictMode>,
	);
})();
