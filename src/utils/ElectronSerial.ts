import { isMatch } from 'es-toolkit/compat';
import { createLogger } from '@swedbank/utils';

export interface PortInfo {
	friendlyName: string;
	locationId: string;
	manufacturer: string;
	path: string;
	pnpId: string;
	productId: string;
	serialNumber: string;
	vendorId: string;
}

const logger = createLogger('ElectronSerial');

export class ElectronSerial {
	private static async listPorts() {
		const ports = await window.api?.serial.list();
		console.log('Ports: ', ports);
		return ports;
	}

	public static async open(connectionPattern: Partial<PortInfo>, baudRate: number) {
		const ports = (await this.listPorts()) as PortInfo[];
		const port = ports.find((port) => isMatch(port, connectionPattern));

		if (!port) {
			logger.error(`Port not found for pattern: `, connectionPattern);
			return;
		}

		await window.api?.serial.open(port.path, baudRate);

		return {
			onMessage: (callback: (msg: string) => void) => {
				return window.api?.serial.onData((msg: { path: string; data: string }) => {
					if (msg.path === port.path) {
						callback(msg.data);
					}
				});
			},
		};
	}
}
