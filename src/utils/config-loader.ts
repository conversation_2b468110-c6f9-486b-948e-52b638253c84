import { getAssetPath } from '@/utils/assets';
import { fetchWithParser } from '@/utils/data-loader.ts';

interface ConfigOptions {
	isApp: boolean;
}

export async function loadConfig<T = any>({ isApp }: ConfigOptions) {
	const config = await fetchWithParser<{ config?: unknown }>(`${isApp ? '..' : ''}/package.json`)
		.then((r) => r.config || {})
		.catch(() => ({}));

	const assetConfig = await fetchWithParser(getAssetPath('config.yml')).catch(() => ({}));

	return Object.assign({}, config, assetConfig) as T;
}
