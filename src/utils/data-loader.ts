import jsYaml from 'js-yaml';

class YamlParser {
	static parse<T>(text: string) {
		return jsYaml.load(text) as T;
	}
}

interface AssetParser {
	parse: <T>(text: string) => T;
}

const parserPatterns: Record<string, AssetParser> = {
	yaml: YamlParser,
	json: JSON,
};

const fileExtensions: Record<string, string[]> = {
	yaml: ['yaml', 'yml'],
	json: ['json', 'geojson'],
};

const fileExtensionPattern = new RegExp(`\\.(${Object.values(fileExtensions).flat().join('|')})$`);

function getParserForExtension(extension: string) {
	const [mappedParser] = Object.entries(fileExtensions).find(([, extensions]) => extensions.includes(extension)) || [];

	if (!mappedParser) {
		throw new Error(`No parser found for ${extension}`);
	}

	return parserPatterns[mappedParser];
}

export async function fetchWithParser<T = unknown>(filename: string) {
	const extension = filename.match(fileExtensionPattern)?.[1];
	if (!extension) {
		throw new Error(`No supported file extension found in ${filename}`);
	}

	const parser = getParserForExtension(extension);
	try {
		const text = await fetchText(filename);
		return parser.parse<T>(text);
	} catch (error) {
		throw new Error(`Failed to load ${filename}`);
	}
}

export async function fetchText(filename: string) {
	try {
		const response = await fetch(filename);
		return response.text();
	} catch (error) {
		throw new Error(`Failed to fetch ${filename}`);
	}
}
