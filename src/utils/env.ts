const truthyValues = ['true', '1', 'yes', 'on'];

export class Env {
	static get(key: string): string | undefined {
		const returnVal = window.api?.env?.[key] || import.meta.env[key];

		// if (returnVal === undefined) {
		// 	throw new Error(`Environment variable ${key} not found`);
		// }

		return returnVal;
	}

	static getNumber(key: string): number {
		return Number(this.get(key));
	}

	static getBoolean(key: string, defaultValue: boolean = false): boolean {
		const keyValue = this.get(key);
		if (keyValue === undefined) return defaultValue;
		return truthyValues.includes(keyValue.toLowerCase());
	}
}
