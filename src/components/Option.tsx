import { useTranslation } from '@platvorm/i18n';
import { CSSProperties, FC, useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import { Card, CardHeader } from './ui/Card';
import { AnswerState } from '@/lib/quiz/rules.ts';
import { OptionState } from '@/hooks/useQuiz.ts';
import { useQuizContext } from '@/contexts/QuizContext';
import useTimeout from '@/hooks/useTimeout';
import { motion } from 'motion/react';
import { headerAnimations, optionAnimations } from '@/lib/quiz/animations';

const OptionContainer = styled(motion.div)`
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: end;
`;

const OptionHeader = styled(CardHeader)`
	margin-bottom: 10px;
	padding: 10px;
	font-family: 'Swedbank Headline', sans-serif;
	font-weight: 900;
	font-size: 84px;
	min-height: 125px;
`;

const Points = styled(motion.div)`
	position: absolute;
	top: -12%;
	left: 50%;
	transform: translateX(-50%);
	color: var(--color-positive);
	font-family: var(--font-headline);
	font-size: 60px;
	font-weight: 900;
	z-index: -1;
`;

interface OptionProps {
	option: OptionState;
	index: number;
	maxCharCount: number;
}

const stateMap: Record<AnswerState, string | null> = {
	[AnswerState.Correct]: 'positive',
	[AnswerState.Incorrect]: 'negative',
	[AnswerState.NotFinished]: 'neutral',
};

export const Option: FC<OptionProps> = ({ option, index, maxCharCount }) => {
	const [active, setActive] = useState(false);
	const { t } = useTranslation();
	const { quizGame } = useQuizContext();
	const { reset: setActiveTimeout } = useTimeout(() => {
		setActive(false);
	}, 500);
	const { scoreCounter } = useQuizContext();

	const cardState = (option.answerState !== null && stateMap[option.answerState]) || null;

	useEffect(() => {
		function handleAnswer(event: CustomEvent) {
			const eventIndex = event.detail.index;
			if (index === eventIndex) {
				setActive(true);
				setActiveTimeout();
			}
		}

		window.addEventListener('answer', handleAnswer, false);

		return () => {
			window.removeEventListener('answer', handleAnswer, false);
		};
	}, [index, option.id, setActiveTimeout]);

	const style: CSSProperties = {
		fontSize: `${maxCharCount > 85 ? 36 : maxCharCount > 10 ? 48 : 60}px`,
	};

	const [headerText, setHeaderText] = useState<string>(option.id);

	const headerStyle: CSSProperties = useMemo(
		() => ({
			fontSize: headerText.length > 10 ? '60px' : '84px',
		}),
		[headerText?.length],
	);

	const { reset: setTryAgainTimeout, clear: clearTryAgainTimeout } = useTimeout(() => {
		setHeaderText(t(`feedback.try-again`));
	}, 2000);

	useEffect(() => {
		if (cardState && cardState !== 'neutral') {
			setHeaderText(t(`feedback.${cardState}`));
		} else {
			setHeaderText(option.id);
		}
	}, [cardState, option.id, t]);

	useEffect(() => {
		if (cardState === 'negative') {
			setTryAgainTimeout();
		} else {
			clearTryAgainTimeout();
		}

		return () => {
			clearTryAgainTimeout();
		};
	}, [cardState, clearTryAgainTimeout, setTryAgainTimeout]);

	return (
		<OptionContainer key={option.text} variants={optionAnimations} transition={{ duration: 0.5 }}>
			{cardState === 'positive' && (
				<Points
					initial={{ x: '-50%', y: 70, scale: 0.9 }}
					animate={{ y: 0, scale: 1 }}
					transition={{ duration: 0.3, delay: 0.5 }}
				>
					+{scoreCounter.lastPoints}
				</Points>
			)}
			<OptionHeader
				key={[headerText, cardState].join('-')}
				variants={headerAnimations}
				animate={
					headerText === 'Proovi veel!'
						? 'shake'
						: cardState === 'negative'
							? ['bounce', 'shake']
							: cardState
								? 'bounce'
								: undefined
				}
				$state={cardState}
				$active={active}
				style={headerStyle}
			>
				{headerText}
			</OptionHeader>
			<Card
				key={cardState}
				animate={{ scale: cardState ? [1, 1.05, 1] : 1 }}
				transition={{ duration: 0.5 }}
				$state={cardState}
				$active={active}
				style={style}
				dangerouslySetInnerHTML={{
					__html: t(`question-${quizGame.currentQuestion.id}.${option.id}`),
				}}
			/>
		</OptionContainer>
	);
};
