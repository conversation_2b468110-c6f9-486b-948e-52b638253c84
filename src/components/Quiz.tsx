import { useQuizContext } from '@/contexts/QuizContext';
import { useTranslation } from '@platvorm/i18n';
import styled from 'styled-components';
import { useBasketEvents } from '@/hooks/useBasketEvents.ts';
import { useMemo } from 'react';
import { getAnswerTemplate } from '@/components/Quiz/Templates/templates.ts';
import { QuizHeader } from './QuizHeader';
import { QuestionText } from '@/components/QuestionText.tsx';
import { motion } from 'motion/react';

export const QuizContainer = styled(motion.div)`
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	flex: 1;
`;

export const Quiz = () => {
	const { t } = useTranslation();
	const { quizGame } = useQuizContext();

	const submitAnswer = (index: number) => {
		const answerSubmittedEvent = new CustomEvent<AnswerSubmittedEventDetails>('answer', { detail: { index } });
		window.dispatchEvent(answerSubmittedEvent);
		quizGame.submitAnswerByIndex(index);
	};

	useBasketEvents({
		onBasket: submitAnswer,
	});

	const Template = useMemo(() => {
		const template = quizGame.currentQuestion.template;
		return getAnswerTemplate(template);
	}, [quizGame.currentQuestion.template]);

	return (
		<>
			<QuizContainer>
				<QuestionText text={t(`question-${quizGame.currentQuestion.id}.text`)} />
				<Template />
			</QuizContainer>
			<QuizHeader />
		</>
	);
};
