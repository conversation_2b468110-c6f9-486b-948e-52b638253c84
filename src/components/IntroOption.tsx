import { useTranslation } from '@platvorm/i18n';
import { CSSProperties, useCallback, useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import { Card, CardHeader } from './ui/Card';
import { AnswerState } from '@/lib/quiz/rules.ts';
import useTimeout from '@/hooks/useTimeout';
import { headerAnimations, optionAnimations } from '@/lib/quiz/animations';
import { motion } from 'motion/react';

const OptionContainer = styled(motion.div)`
	display: flex;
	flex-direction: column;
	justify-content: end;
`;

const OptionHeader = styled(CardHeader)`
	margin-bottom: 10px;
	padding: 10px;
	font-family: 'Swedbank Headline', sans-serif;
	font-weight: 900;
	font-size: 84px;
	min-height: 125px;
`;

interface OptionProps {
	option: { id: string; text: string };
	index: number;
	currentSlide: IntroSlide;
	optionState: AnswerState | null;
}

const stateMap: Record<AnswerState, string | null> = {
	[AnswerState.Correct]: 'positive',
	[AnswerState.Incorrect]: 'negative',
	[AnswerState.NotFinished]: 'neutral',
};

export function IntroOption({ option, index, currentSlide, optionState }: OptionProps) {
	const { t } = useTranslation();

	const handleAnswer = useCallback(
		(event: CustomEvent) => {
			if (event.detail.index === index) {
				currentSlide.onThrow(option.id);
			}
		},
		[currentSlide, index, option.id],
	);

	useEffect(() => {
		if (optionState === AnswerState.Correct) {
			currentSlide.onCorrect?.();
		}
	}, [currentSlide, optionState]);

	useEffect(() => {
		window.addEventListener('answer', handleAnswer, false);

		return () => {
			window.removeEventListener('answer', handleAnswer, false);
		};
	}, [handleAnswer, index, option.id]);

	const cardState = useMemo(() => (optionState !== null && stateMap[optionState]) || null, [optionState]);

	const [headerText, setHeaderText] = useState<string>(option.id);

	const headerStyle: CSSProperties = useMemo(
		() => ({
			fontSize: headerText.length > 10 ? '60px' : '84px',
		}),
		[headerText.length],
	);

	const { reset: setTryAgainTimeout, clear: clearTryAgainTimeout } = useTimeout(() => {
		setHeaderText(t(`feedback.try-again`));
	}, 2000);

	useEffect(() => {
		if (cardState && cardState !== 'neutral') {
			setHeaderText(t(`feedback.${cardState}`));
		} else {
			setHeaderText(option.id);
		}
	}, [cardState, option.id, t]);

	useEffect(() => {
		if (cardState === 'negative') {
			setTryAgainTimeout();
		} else {
			clearTryAgainTimeout();
		}

		return () => {
			clearTryAgainTimeout();
		};
	}, [cardState, clearTryAgainTimeout, setTryAgainTimeout]);

	return (
		<OptionContainer key={option.text} variants={optionAnimations} transition={{ duration: 0.5 }}>
			<OptionHeader
				key={[headerText, cardState].join('-')}
				variants={headerAnimations}
				animate={
					headerText === 'Proovi veel!'
						? 'shake'
						: cardState === 'negative'
							? ['bounce', 'shake']
							: cardState
								? 'bounce'
								: undefined
				}
				$state={cardState}
				style={headerStyle}
			>
				{headerText}
			</OptionHeader>
			<Card
				key={cardState}
				animate={{ scale: cardState ? [1, 1.05, 1] : 1 }}
				transition={{ duration: 0.5 }}
				$state={cardState}
				style={{
					fontSize: '48px',
					minHeight: '150px',
				}}
				dangerouslySetInnerHTML={{
					__html: option.text,
				}}
			/>
		</OptionContainer>
	);
}
