import styled from 'styled-components';
import { CSSProperties } from 'react';
import { AnimatePresence, motion } from 'motion/react';

export const QuestionWrapper = styled(motion.div)`
	text-align: center;
	margin: 0 auto;
	max-width: 1700px;

	font-family: 'Swedbank Headline', sans-serif;
	font-weight: 700;
	line-height: 1.2;

	strong {
		font-weight: 900;
	}
`;

interface QuestionTextProps {
	text: string;
	className?: string;
}

export function QuestionText({ text, className }: QuestionTextProps) {
	const textLength = text.length;

	const style: CSSProperties = {
		fontSize: `${textLength > 150 ? 48 : 60}px`,
	};

	return (
		<AnimatePresence mode="wait">
			<QuestionWrapper
				key={text}
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				exit={{ opacity: 0 }}
				style={style}
				className={className}
				dangerouslySetInnerHTML={{ __html: text }}
			/>
		</AnimatePresence>
	);
}
