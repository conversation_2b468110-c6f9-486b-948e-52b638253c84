import { motion } from 'motion/react';
import styled from 'styled-components';

interface BoxProps {
	$state?: string | null;
	$active?: boolean;
}

const Box = styled(motion.div)<BoxProps>`
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 32px;
	padding: 24px 32px;
	text-align: center;
	line-height: 1.25;
	font-weight: 700;
	flex: 1;
	max-height: 540px;

	background-color: ${({ $state }) => ($state ? `var(--color-${$state})` : 'white')};
	color: ${({ $state }) => `var(--${!$state || $state === 'neutral' ? 'text' : 'inverted-text'})`};
`;

export const CardHeader = styled(Box)`
	flex: 0;
	max-height: unset;
	font-size: 84px;
	${({ $state }) => $state === null && 'background-color: transparent;'}
`;

export const Card = styled(Box)`
	font-size: 60px;

	p {
		margin: 0;
	}
`;
