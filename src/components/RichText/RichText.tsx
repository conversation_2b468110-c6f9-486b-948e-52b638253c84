interface RichTextNode {
	type: 'root' | 'text' | 'paragraph';
	children?: RichTextNode[];
	[k: string]: any;
}

interface ElementProps {
	node: RichTextNode;
}

function RootElement({ node }: ElementProps) {
	return <RichTextTrees nodes={node.children || []} />;
}

function TextElement({ node }: ElementProps) {
	return node.text;
}

function ParagraphElement({ node }: ElementProps) {
	return (
		<p>
			<RichTextTrees nodes={node.children || []} />
		</p>
	);
}

const elements = {
	root: RootElement,
	text: TextElement,
	paragraph: ParagraphElement,
};

interface RichTextTreesProps {
	nodes: RichTextNode[];
}

export function RichTextTrees({ nodes }: RichTextTreesProps) {
	return nodes.map((node) => {
		return <RichTextTree node={node} />;
	});
}

interface RichTextTreeProps {
	node: RichTextNode;
}

export function RichTextTree({ node }: RichTextTreeProps) {
	const Wrapper = elements[node.type];

	if (!Wrapper) {
		console.error(`${node.type} wrapper element is not implemented`);
		return null;
	}

	return <Wrapper node={node} />;
}
