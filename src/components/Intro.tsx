import { useTranslation } from '@platvorm/i18n';
import { QuizContainer } from './Quiz';
import { QuestionOptions } from './Quiz/Templates/QuizTemplate';
import styled from 'styled-components';
import { IntroOption } from './IntroOption';
import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useBasketEvents } from '@/hooks/useBasketEvents';
import { AnswerState, Rule, rules } from '@/lib/quiz/rules';
import { IncorrectRule } from '@/lib/quiz/rules/IncorrectRule';
import { QuestionWrapper } from './QuestionText';
import { useTimer } from '@/hooks/useTimer.ts';
import { delay } from '@/utils/delay.ts';
import { IntroTimer } from './IntroTimer';
import { AnimatePresence } from 'motion/react';
import { optionContainerAnimations } from '@/lib/quiz/animations';

const IntroTextWrapper = styled(QuestionWrapper)`
	color: var(--swedbank-orange-text);
	flex: 2;
	display: flex;
	flex-direction: column;
	justify-content: center;
	max-width: 1200px;
	font-size: 60px;

	p {
		margin: 0 0 64px 0;
	}
`;

interface IntroTextProps {
	content: string;
}

const IntroText: FC<IntroTextProps> = ({ content }) => {
	return (
		<IntroTextWrapper
			key={content}
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			exit={{ opacity: 0 }}
			dangerouslySetInnerHTML={{ __html: content }}
		/>
	);
};

const IntroOptions = styled(QuestionOptions)`
	height: auto;
`;

interface IntroProps {
	introTimeLimit: number;
	onExit?: () => void;
}

export function Intro({ introTimeLimit, onExit }: IntroProps) {
	const { t } = useTranslation();

	const [slideIndex, setSlideIndex] = useState(0);
	const [submittedAnswers, setSubmittedAnswers] = useState<string[]>([]);
	const [isLocked, setIsLocked] = useState(false);

	const { start: startIntroTimer, seconds: introTimeLeft } = useTimer(onExit);

	useEffect(() => {
		startIntroTimer(introTimeLimit);
	}, [introTimeLimit, startIntroTimer]);

	const lockAnswer = useCallback(() => {
		setIsLocked(true);
	}, []);

	const resetQuestion = useCallback(() => {
		setIsLocked(false);
		setSubmittedAnswers([]);
	}, []);

	const nextSlide = useCallback(() => {
		setSlideIndex((prev) => prev + 1);
	}, []);

	const slides: IntroSlide[] = useMemo(
		() => [
			{
				id: 'intro-1',
				correctOptions: [0, 1, 2, 3],
				options: [
					{ id: 'A', text: 'Edasi' },
					{ id: 'B', text: 'Edasi' },
					{ id: 'C', text: 'Edasi' },
					{ id: 'D', text: 'Edasi' },
				],
				rule: {
					type: 'free',
				},
				onThrow: (optionId?: string) => {
					if (!optionId) return;
					setSubmittedAnswers((prev) => [...prev, optionId]);
				},
				onCorrect: async () => {
					if (isLocked) return;
					lockAnswer();
					await delay(2000);
					nextSlide();
					resetQuestion();
				},
			},
			{
				id: 'intro-2',
				confirmText: t('intro.confirm'),
				correctText: t('intro.correct'),
				correctOptions: [2],
				options: [
					{ id: 'A', text: 'Vale' },
					{ id: 'B', text: 'Vale' },
					{ id: 'C', text: 'Õige' },
					{ id: 'D', text: 'Vale' },
				],
				rule: {
					type: 'confirm',
					params: {
						id: 'C',
						count: 2,
					},
				},
				onThrow: (optionId?: string) => {
					if (!optionId) return;
					setSubmittedAnswers((prev) => [...prev, optionId]);
				},
				onCorrect: async () => {
					if (isLocked) return;
					lockAnswer();
					await delay(2000);
					nextSlide();
					resetQuestion();
				},
			},
			{
				id: 'intro-3',
				correctOptions: [0, 1, 2, 3],
				options: [
					{ id: 'A', text: 'Alusta' },
					{ id: 'B', text: 'Alusta' },
					{ id: 'C', text: 'Alusta' },
					{ id: 'D', text: 'Alusta' },
				],
				rule: {
					type: 'confirm',
					params: {
						id: ['A', 'B', 'C', 'D'],
						count: 2,
					},
				},
				onThrow: (optionId?: string) => {
					if (!optionId) return;
					setSubmittedAnswers((prev) => [...prev, optionId]);
				},
				onCorrect: async () => {
					if (isLocked) return;
					lockAnswer();
					await delay(2000);
					onExit?.();
				},
			},
		],
		[isLocked, lockAnswer, nextSlide, onExit, resetQuestion, t],
	);

	const currentSlide = useMemo(() => slides[slideIndex], [slideIndex, slides]);

	const validationRule: Rule = useMemo(() => {
		if (!currentSlide?.rule) return new IncorrectRule();

		const { type, params } = currentSlide.rule;
		const factory = rules[type];

		if (!factory) return new IncorrectRule();

		return factory(params);
	}, [currentSlide]);

	const currentAnswerState = useMemo(() => {
		return validationRule.validate(submittedAnswers);
	}, [submittedAnswers, validationRule]);

	const submitAnswer = useCallback(
		(index: number) => {
			if (isLocked || currentAnswerState === AnswerState.Correct) return;
			const answerSubmittedEvent = new CustomEvent<AnswerSubmittedEventDetails>('answer', { detail: { index } });
			window.dispatchEvent(answerSubmittedEvent);
		},
		[currentAnswerState, isLocked],
	);

	useBasketEvents({
		onBasket: submitAnswer,
	});

	return (
		<>
			<QuizContainer>
				<AnimatePresence mode="wait">
					{currentSlide.confirmText && submittedAnswers.length && currentAnswerState === AnswerState.NotFinished ? (
						<IntroText content={currentSlide.confirmText} />
					) : currentSlide.correctText && submittedAnswers.length && currentAnswerState === AnswerState.Correct ? (
						<IntroText content={currentSlide.correctText} />
					) : (
						<IntroText content={t(`${currentSlide.id}.text`)} />
					)}
				</AnimatePresence>
				<AnimatePresence mode="wait">
					<IntroOptions key={currentSlide.id} variants={optionContainerAnimations} initial="hidden" animate="visible">
						{slides[slideIndex].options.map((option, index) => (
							<IntroOption
								key={option.id}
								option={option}
								index={index}
								currentSlide={currentSlide}
								optionState={submittedAnswers[submittedAnswers.length - 1] === option.id ? currentAnswerState : null}
							/>
						))}
					</IntroOptions>
				</AnimatePresence>
			</QuizContainer>
			<IntroTimer timeLeft={introTimeLeft} />
		</>
	);
}
