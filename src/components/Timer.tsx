import styled from 'styled-components';
import { useQuizContext } from '@/contexts/QuizContext.tsx';

const TimerContainer = styled.div``;

export const Timer = () => {
	const { totalGameTimeLeft } = useQuizContext();

	const timeAsString = () => {
		const minutes = Math.floor(totalGameTimeLeft / 60);
		const seconds = totalGameTimeLeft % 60;
		return `${minutes}:${seconds.toString().padStart(2, '0')}`;
	};

	return <TimerContainer>{timeAsString()}</TimerContainer>;
};
