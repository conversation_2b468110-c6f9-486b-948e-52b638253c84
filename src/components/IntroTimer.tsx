import { FC } from 'react';
import styled from 'styled-components';
import { HeaderContainer } from './QuizHeader';

const TimerContainer = styled.div`
	margin-left: auto;

	display: flex;
	font-weight: 700;
	font-family: 'Swedbank Headline', sans-serif;
	font-weight: 900;
	font-size: 72px;
	line-height: 1;

	span {
		margin-right: 16px;
	}

	@media (max-width: 2000px) {
		font-size: 36px;
	}
`;

const Time = styled.div`
	color: var(--time);
`;

interface TimerProps {
	timeLeft: number;
}

export const IntroTimer: FC<TimerProps> = ({ timeLeft }) => {
	const timeAsString = () => {
		const minutes = Math.floor(timeLeft / 60);
		const seconds = timeLeft % 60;
		return `${minutes}:${seconds.toString().padStart(2, '0')}`;
	};

	return (
		<HeaderContainer>
			<TimerContainer>
				<span>Mängu alguseni aega:</span>
				<Time>{timeAsString()}</Time>
			</TimerContainer>
		</HeaderContainer>
	);
};
