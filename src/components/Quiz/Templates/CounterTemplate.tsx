import { useQuizContext } from '@/contexts/QuizContext.tsx';
import { useMemo } from 'react';
import styled, { css } from 'styled-components';

const Wrapper = styled.div`
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
`;

interface CounterProps {
	$expanded: boolean;
}

const Counter = styled.div<CounterProps>`
	font-size: 20rem;
	font-weight: 700;
	color: var(--swedbank-orange-text);
	transition: 200ms all;

	${({ $expanded }) =>
		$expanded &&
		css`
			font-size: 24rem;
		`}
`;

const Timer = styled.div`
	font-size: 60px;
	font-weight: 700;
	color: var(--swedbank-orange-text);
`;

export function CounterTemplate() {
	const { quizGame, questionTimeLeft, questionTimeUp } = useQuizContext();

	const total = useMemo(() => {
		return quizGame.submittedAnswers.length;
	}, [quizGame.submittedAnswers]);

	return (
		<Wrapper>
			<Counter $expanded={questionTimeUp}>{total}</Counter>
			{!questionTimeUp && <Timer>{questionTimeLeft}</Timer>}
		</Wrapper>
	);
}
