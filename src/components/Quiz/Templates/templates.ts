import { QuizTemplate } from '@/components/Quiz/Templates/QuizTemplate.tsx';
import { CounterTemplate } from '@/components/Quiz/Templates/CounterTemplate.tsx';
import { ReactElement } from 'react';

const answerTemplates: Record<string, () => ReactElement> = {
	default: QuizTemplate,
	counter: CounterTemplate,
};

export function getAnswerTemplate(template?: string) {
	return answerTemplates[template || 'default'];
}
