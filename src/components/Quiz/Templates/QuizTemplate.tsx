import { useQuizContext } from '@/contexts/QuizContext.tsx';
import { Option } from '@/components/Option.tsx';
import styled from 'styled-components';
import { useTranslation } from '@platvorm/i18n';
import { useMemo } from 'react';
import { AnimatePresence, motion } from 'motion/react';
import { optionContainerAnimations } from '@/lib/quiz/animations';

export const QuestionOptions = styled(motion.div)`
	display: grid;
	grid-template-columns: repeat(4, 420px);
	grid-gap: 40px;
	height: 625px;
`;

export function QuizTemplate() {
	const { t } = useTranslation();
	const { quizGame } = useQuizContext();

	const maxCharCount = useMemo(
		() =>
			Math.max(
				...quizGame.optionsState.map((option) => t(`question-${quizGame.currentQuestion.id}.${option.id}`).length),
			),
		[quizGame.optionsState, quizGame.currentQuestion.id, t],
	);

	return (
		<AnimatePresence mode="wait">
			<QuestionOptions
				key={quizGame.currentQuestion.id}
				variants={optionContainerAnimations}
				initial="hidden"
				animate="visible"
			>
				{quizGame.optionsState.map((option, index) => (
					<Option key={option.id} option={option} index={index} maxCharCount={maxCharCount} />
				))}
			</QuestionOptions>
		</AnimatePresence>
	);
}
