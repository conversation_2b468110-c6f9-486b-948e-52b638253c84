import { useQuizContext } from '@/contexts/QuizContext';
import { Timer } from '@swedbank/components';
import styled from 'styled-components';

export const HeaderContainer = styled.div`
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 25px 32px 20px;
	background-color: white;

	font-family: 'Swedbank Headline', sans-serif;
	font-size: 36px;
	line-height: 1;
	font-weight: 700;

	display: flex;
	justify-content: space-between;
`;

const ScoreContainer = styled.div`
	display: flex;
	gap: 48px;
`;

const AnswerCount = styled.span`
	color: var(--alert-green);
	font-weight: 900;
`;

const ScoreCount = styled.span`
	color: var(--alert-green);
	font-weight: 900;
`;

export function QuizHeader() {
	const { totalGameTimeLeft, scoreCounter } = useQuizContext();

	return (
		<HeaderContainer>
			<ScoreContainer>
				<div>
					Küsimusi vastatud: <AnswerCount>{scoreCounter.answerCounts.confirm || 0}</AnswerCount>
				</div>
				<div>
					Punktid: <ScoreCount>{scoreCounter.score}</ScoreCount>
				</div>
			</ScoreContainer>
			<Timer timeLeft={totalGameTimeLeft} />
		</HeaderContainer>
	);
}
