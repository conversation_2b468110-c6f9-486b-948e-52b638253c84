import { useQuizContext } from '@/contexts/QuizContext.tsx';
import { useTranslation } from '@platvorm/i18n';
import { Text } from '@/components/Typography/Text.tsx';
import { CenterContainer } from '@/components/CenterContainer.tsx';

export function Outro() {
	const { scoreCounter } = useQuizContext();
	const { t } = useTranslation();

	return (
		<CenterContainer>
			<h1>{t('outro.game-over')}</h1>

			<div>
				{t('outro.answers-count.confirm')} <Text $variant={'positive'}>{scoreCounter.answerCounts.confirm || 0}</Text>
			</div>
			<div>
				{t('outro.answers-count.free')} <Text $variant={'positive'}>{scoreCounter.answerCounts.free || 0}</Text>
			</div>

			<h2>
				{t('outro.score')} {scoreCounter.score}
			</h2>
		</CenterContainer>
	);
}
