import { stagger } from 'motion';

export const optionAnimations = {
	hidden: { opacity: 0, y: 700 },
	visible: { opacity: 1, y: 0 },
};

export const headerAnimations = {
	bounce: {
		y: [0, -20, 0],
		transition: { duration: 0.4 },
	},
	shake: {
		x: [-10, 10, -10, 10, -10, 0],
		transition: { delay: 0.4, duration: 0.4 },
	},
};

export const optionContainerAnimations = {
	hidden: { opacity: 0, transition: { staggerChildren: 0.1 } },
	visible: {
		opacity: 1,
		transition: {
			delayChildren: stagger(0.1),
		},
	},
};
