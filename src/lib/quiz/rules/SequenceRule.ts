import { AnswerState, Rule } from '@/lib/quiz/rules.ts';

type SequenceParams = string[];

function hasEnoughAnswers(answers: string[], sequenceParams: SequenceParams) {
	return answers.length >= sequenceParams.length;
}

function hasInvalidAnswer(answers: string[], sequenceParams: SequenceParams) {
	const sequenceString = sequenceParams.join('');
	const answersString = answers.join('');
	return !sequenceString.startsWith(answersString);
}


export class SequenceRule implements Rule {
	constructor(private params: SequenceParams) {}

	validate(answers: string[]) {
		if (hasInvalidAnswer(answers, this.params)) return AnswerState.Incorrect;
		if (!hasEnoughAnswers(answers, this.params)) return AnswerState.NotFinished;
		return AnswerState.Correct;
	}
}
