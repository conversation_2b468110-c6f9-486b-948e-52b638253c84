import { AnswerState, Rule } from '@/lib/quiz/rules.ts';

type ConfirmParams = { id: ID; count: number };

function hasValidLength(answers: string[], params: ConfirmParams) {
	return answers.length >= params.count;
}

function hasLastValuesSame(answers: string[], params: ConfirmParams) {
	const lastValues = answers.slice(-params.count);
	const checkValue = lastValues[0];
	if (!checkValue) return false;

	return lastValues.every((value) => value === checkValue);
}

function hasLastValuesCorrect(answers: string[], params: ConfirmParams) {
	const lastValues = answers.slice(-params.count);
	return lastValues.every((value) => value === params.id || params.id.includes(value));
}

// Keep readability in mind when writing code as rules may become complex
// and hard to understand when written in a single function
// Performance is not a concern in this case (yet)
export class ConfirmRule implements Rule {
	constructor(private params: ConfirmParams) {}

	validate(answers: string[]) {
		if (!hasValidLength(answers, this.params)) return AnswerState.NotFinished;
		if (!hasLastValuesSame(answers, this.params)) return AnswerState.NotFinished;
		if (!hasLastValuesCorrect(answers, this.params)) return AnswerState.Incorrect;
		return AnswerState.Correct;
	}
}
