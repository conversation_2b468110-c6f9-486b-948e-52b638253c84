import { AnswerState, Rule } from '@/lib/quiz/rules.ts';
import { every, sum } from 'lodash';

type CountParams = { id: ID; value: number }[];

function hasInvalidAnswer(answers: string[], params: CountParams) {
	const invalidAnswers = answers.filter((answer) => !params.some((param) => param.id === answer));
	return invalidAnswers.length > 0;
}

function hasCountsFilled(answers: string[], params: CountParams) {
	const filledCounts = params.map((param) => {
		const count = answers.filter((answer) => answer === param.id).length;
		return count === param.value;
	});

	return every(filledCounts);
}

function hasEnoughAnswers(answers: string[], params: CountParams) {
	const requiredTotalCount = sum(params.map((param) => param.value));
	return answers.length >= requiredTotalCount;
}

export class CountRule implements Rule {
	constructor(private params: CountParams) {}

	validate(answers: string[]) {
		if (hasInvalidAnswer(answers, this.params)) return AnswerState.Incorrect; // Fail when answers contain an invalid answer
		if (!hasEnoughAnswers(answers, this.params)) return AnswerState.NotFinished; // Not enough answers yet to validate and rules after this are not checked
		if (!hasCountsFilled(answers, this.params)) return AnswerState.Incorrect; // Counts not filled correctly

		return AnswerState.Correct;
	}
}
