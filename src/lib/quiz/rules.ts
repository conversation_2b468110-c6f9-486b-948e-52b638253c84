import { CountRule } from '@/lib/quiz/rules/CountRule.ts';
import { SequenceRule } from '@/lib/quiz/rules/SequenceRule.ts';
import { ConfirmRule } from '@/lib/quiz/rules/ConfirmRule.ts';
import { FreeRule } from '@/lib/quiz/rules/FreeRule.ts';

export enum AnswerState {
	NotFinished,
	Incorrect,
	Correct,
}

export type ValidateFunction = (answers: string[]) => AnswerState;
export type ValidationFactory = (args: any) => Rule;

export interface Rule {
	validate: ValidateFunction;
	// setParam(param: any): void;
}

export const rules: Record<string, ValidationFactory> = {
	count: (params: any) => new CountRule(params),
	sequence: (params: any) => new SequenceRule(params),
	confirm: (params: any) => new ConfirmRule(params),
	free: () => new FreeRule(),
};
