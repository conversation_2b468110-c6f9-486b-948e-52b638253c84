import { useCallback, useMemo, useState } from 'react';
import { groupBy, reduce } from 'lodash';

type AnswerStats = Record<ValidationRuleType, number>;

interface AnsweredQuestion {
	question: Question;
	answerCounts: AnswerStats;
}

type ScoringRule = (score: number, answeredQuestion: AnsweredQuestion) => number;

const points = {
	free: 1,
	confirm: 3,
};

const scoringRules: Record<ValidationRuleType, ScoringRule> = {
	free: (score) => score + points.free,
	confirm: (score) => score + points.confirm,
};

function calcScoreForAnswers(answers: AnsweredQuestion[]) {
	return reduce(
		answers,
		(score, answeredQuestion) => {
			const scoringRule = scoringRules[answeredQuestion.question.rule.type];
			if (!scoringRule) {
				console.warn(`No scoring rule for: "${answeredQuestion.question.rule.type}"`);
				return score;
			}
			return scoringRule(score, answeredQuestion);
		},
		0,
	);
}

export function useScoreCounter() {
	const [answeredQuestions, setAnsweredQuestions] = useState<AnsweredQuestion[]>([]);
	const [lastPoints, setLastPoints] = useState<number>(0);

	const groupedAnswers = useMemo(() => {
		return groupBy(answeredQuestions, (d) => d.question.rule.type);
	}, [answeredQuestions]);

	const answerCounts = useMemo(() => {
		return reduce(
			groupedAnswers,
			(acc, answers, key) => ({
				...acc,
				[key]: answers.length,
			}),
			{} as AnswerStats,
		);
	}, [groupedAnswers]);

	const answerScores = useMemo(() => {
		return reduce(
			groupedAnswers,
			(acc, answers, key) => ({
				...acc,
				[key]: calcScoreForAnswers(answers),
			}),
			{} as AnswerStats,
		);
	}, [groupedAnswers]);

	const score = useMemo(() => {
		return reduce(answerScores, (sum, value) => sum + value, 0);
	}, [answerScores]);

	const addAnswer = useCallback((answeredQuestion: AnsweredQuestion) => {
		setAnsweredQuestions((prev) => [...prev, answeredQuestion]);
		setLastPoints(points[answeredQuestion.question.rule.type]);
	}, []);

	return { score, addAnswer, answerScores, answerCounts, lastPoints } as const;
}
