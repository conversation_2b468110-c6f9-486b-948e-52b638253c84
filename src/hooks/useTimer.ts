import { useCallback, useEffect, useRef, useState } from 'react';

export function useTimer(callback?: () => void) {
	const intervalTimer = useRef<NodeJS.Timeout | null>(null);
	const callbackRef = useRef<(() => void) | undefined>(callback);
	const [seconds, setSeconds] = useState(0);

	// Update the callbackRef whenever the callback changes
	useEffect(() => {
		callbackRef.current = callback;
	}, [callback]);

	const stop = useCallback(() => {
		if (intervalTimer.current) {
			clearInterval(intervalTimer.current);
			intervalTimer.current = null;
		}
		setSeconds(0);
	}, []);

	const start = useCallback(
		(seconds: number) => {
			if (intervalTimer.current) return;

			setSeconds(seconds);
			intervalTimer.current = setInterval(() => {
				setSeconds((prev) => {
					if (prev === 0) {
						stop();
						callbackRef.current?.();
						return 0;
					}
					return prev - 1;
				});
			}, 1000);
		},
		[stop],
	);

	useEffect(() => {
		return () => {
			stop();
		};
	}, [stop]);

	return { start, stop, seconds };
}
