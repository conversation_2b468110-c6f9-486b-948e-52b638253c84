import { useCallback, useEffect, useRef } from 'react';
import { ElectronSerial } from '@/utils/ElectronSerial.ts';
import { useKeyEvents } from '@/hooks/useKeyEvents.ts';
import { useRootContext } from '@/contexts/RootContext.tsx';

interface BasketEvent {
	onBasket?: (basketIndex: number) => void;
}

type BasketEventHandler = (basketIndex: number) => void;

export function useBasketEvents({ onBasket }: BasketEvent) {
	const { config } = useRootContext();

	useEffect(() => {
		console.log('ob');
	}, [onBasket]);

	const handleBasketEvent = useCallback(
		(basketIndex: number) => {
			onBasket?.(basketIndex);
		},
		[onBasket],
	);

	useKeyEvents({
		'1': () => handleBasketEvent(0),
		'2': () => handleBasketEvent(1),
		'3': () => handleBasketEvent(2),
		'4': () => handleBasketEvent(3),
	});

	const handleSerialData = useCallback(
		(data: string) => {
			const eventFunctions: Record<string, BasketEventHandler> = {
				B: handleBasketEvent,
			};

			const [event, args] = data.split(':');

			if (eventFunctions[event]) {
				const basketIndex = parseInt(args);
				eventFunctions[event](basketIndex);
			}
		},
		[handleBasketEvent],
	);

	const unsubscribe = useRef<() => void>();
	const serialPortPath = useRef<string>();
	const isMounted = useRef(true);

	useEffect(() => {
		const { connectionPattern, baudRate } = config.serialConnection?.sensors || {};

		async function openSerial() {
			try {
				if (!connectionPattern || !baudRate) {
					console.error('No serial connection pattern or baud rate found in config');
					return;
				}

				const port = await ElectronSerial.open(connectionPattern, baudRate);

				// Check if component is still mounted after async operation
				if (!isMounted.current || !port) return;

				// Store the port path for cleanup
				const ports = await window.api?.serial.list();
				const matchedPort = ports?.find((p: any) =>
					Object.entries(connectionPattern).every(([key, value]) => p[key] === value)
				);
				if (matchedPort) {
					serialPortPath.current = matchedPort.path;
				}

				unsubscribe.current = port.onMessage((msg: string) => {
					console.log('Serial data: ', msg.trim());
					handleSerialData(msg.trim());
				});
			} catch (error) {
				console.error('Error opening serial port: ', error);
			}
		}

		isMounted.current = true;
		openSerial();

		return () => {
			isMounted.current = false;

			// Unsubscribe from message listener
			unsubscribe.current?.();

			// Close the serial port if it was opened
			if (serialPortPath.current) {
				window.api?.serial.close(serialPortPath.current).catch((error) => {
					console.error('Error closing serial port during cleanup:', error);
				});
			}

			// Clear refs
			unsubscribe.current = undefined;
			serialPortPath.current = undefined;
		};
	}, [config, handleSerialData]);

	return null;
}
