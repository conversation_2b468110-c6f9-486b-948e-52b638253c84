import { useCallback, useEffect, useRef } from 'react';
import { ElectronSerial } from '@/utils/ElectronSerial.ts';
import { useKeyEvents } from '@/hooks/useKeyEvents.ts';
import { useRootContext } from '@/contexts/RootContext.tsx';

interface BasketEvent {
	onBasket?: (basketIndex: number) => void;
}

type BasketEventHandler = (basketIndex: number) => void;

export function useBasketEvents({ onBasket }: BasketEvent) {
	const { config } = useRootContext();

	useEffect(() => {
		console.log('ob');
	}, [onBasket]);

	const handleBasketEvent = useCallback(
		(basketIndex: number) => {
			onBasket?.(basketIndex);
		},
		[onBasket],
	);

	useKeyEvents({
		'1': () => handleBasketEvent(0),
		'2': () => handleBasketEvent(1),
		'3': () => handleBasketEvent(2),
		'4': () => handleBasketEvent(3),
	});

	const handleSerialData = useCallback(
		(data: string) => {
			const eventFunctions: Record<string, BasketEventHandler> = {
				B: handleBasketEvent,
			};

			const [event, args] = data.split(':');

			if (eventFunctions[event]) {
				const basketIndex = parseInt(args);
				eventFunctions[event](basketIndex);
			}
		},
		[handleBasketEvent],
	);

	const handleSerialDataRef = useRef(handleSerialData);
	const unsubscribe = useRef<() => void>();
	const serialPortPath = useRef<string>();
	const isMounted = useRef(true);

	// Update the ref when the callback changes
	useEffect(() => {
		handleSerialDataRef.current = handleSerialData;
	}, [handleSerialData]);

	useEffect(() => {
		const { connectionPattern, baudRate } = config.serialConnection?.sensors || {};

		async function openSerial() {
			try {
				// Clean up any existing connection first
				if (unsubscribe.current) {
					console.log('Cleaning up existing serial connection before opening new one');
					unsubscribe.current();
					unsubscribe.current = undefined;
				}

				if (serialPortPath.current) {
					await window.api?.serial.close(serialPortPath.current).catch((error) => {
						console.error('Error closing existing serial port:', error);
					});
					serialPortPath.current = undefined;
				}

				if (!connectionPattern || !baudRate) {
					console.error('No serial connection pattern or baud rate found in config');
					return;
				}

				// Find the port first to get the path
				const ports = await window.api?.serial.list();
				const matchedPort = ports?.find((p: any) =>
					Object.entries(connectionPattern).every(([key, value]) => p[key] === value)
				);

				if (!matchedPort) {
					console.error('Port not found for pattern:', connectionPattern);
					return;
				}

				// Check if component is still mounted before proceeding
				if (!isMounted.current) return;

				// Store the port path for cleanup
				serialPortPath.current = matchedPort.path;

				// Open the port
				const success = await window.api?.serial.open(matchedPort.path, baudRate);
				if (!success) {
					console.error('Failed to open serial port');
					return;
				}

				// Check again if component is still mounted after async operation
				if (!isMounted.current) return;

				// Set up the message listener
				unsubscribe.current = window.api?.serial.onData((msg: { path: string; data: string }) => {
					if (msg.path === matchedPort.path) {
						console.log('Serial data: ', msg.data.trim());
						handleSerialDataRef.current(msg.data.trim());
					}
				});

			} catch (error) {
				console.error('Error opening serial port: ', error);
			}
		}

		isMounted.current = true;
		openSerial();

		return () => {
			isMounted.current = false;

			// Unsubscribe from message listener
			if (unsubscribe.current) {
				console.log('Cleaning up serial connection on unmount');
				unsubscribe.current();
				unsubscribe.current = undefined;
			}

			// Close the serial port if it was opened
			if (serialPortPath.current) {
				window.api?.serial.close(serialPortPath.current).catch((error) => {
					console.error('Error closing serial port during cleanup:', error);
				});
				serialPortPath.current = undefined;
			}
		};
	}, [config]);

	return null;
}
