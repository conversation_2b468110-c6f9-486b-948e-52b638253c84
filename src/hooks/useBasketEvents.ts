import { useCallback, useEffect, useRef } from 'react';
import { ElectronSerial } from '@/utils/ElectronSerial.ts';
import { useKeyEvents } from '@/hooks/useKeyEvents.ts';
import { useRootContext } from '@/contexts/RootContext.tsx';

interface BasketEvent {
	onBasket?: (basketIndex: number) => void;
}

type BasketEventHandler = (basketIndex: number) => void;

export function useBasketEvents({ onBasket }: BasketEvent) {
	const { config } = useRootContext();

	useEffect(() => {
		console.log('ob');
	}, [onBasket]);

	const handleBasketEvent = useCallback(
		(basketIndex: number) => {
			onBasket?.(basketIndex);
		},
		[onBasket],
	);

	useKeyEvents({
		'1': () => handleBasketEvent(0),
		'2': () => handleBasketEvent(1),
		'3': () => handleBasketEvent(2),
		'4': () => handleBasketEvent(3),
	});

	const handleSerialData = useCallback(
		(data: string) => {
			const eventFunctions: Record<string, BasketEventHandler> = {
				B: handleBasketEvent,
			};

			const [event, args] = data.split(':');

			if (eventFunctions[event]) {
				const basketIndex = parseInt(args);
				eventFunctions[event](basketIndex);
			}
		},
		[handleBasketEvent],
	);

	const unsubscribe = useRef<() => void>();

	useEffect(() => {
		const { connectionPattern, baudRate } = config.serialConnection?.sensors || {};

		async function openSerial() {
			try {
				if (!connectionPattern || !baudRate) {
					console.error('No serial connection pattern or baud rate found in config');
					return;
				}

				const port = await ElectronSerial.open(connectionPattern, baudRate);
				if (!port) return;

				unsubscribe.current = port.onMessage((msg: string) => {
					console.log('Serial data: ', msg.trim());
					handleSerialData(msg.trim());
				});
			} catch (error) {
				console.error('Error opening serial port: ', error);
			}
		}
		openSerial();

		return () => {
			unsubscribe.current?.();
		};
	}, [config, handleSerialData]);

	return null;
}
