import { useCallback, useEffect, useRef } from 'react';

export default function useTimeout(callback: () => void, delay: number) {
	const callbackRef = useRef(callback);
	const timeoutRef = useRef<NodeJS.Timeout | null>(null);

	useEffect(() => {
		callbackRef.current = callback;
	}, [callback]);

	const clear = useCallback(() => {
		timeoutRef.current && clearTimeout(timeoutRef.current);
		timeoutRef.current = null;
	}, []);

	const set = useCallback(() => {
		if (timeoutRef.current) return;
		timeoutRef.current = setTimeout(() => {
			callbackRef.current();
			clear();
		}, delay);
	}, [clear, delay]);

	useEffect(() => {
		return clear;
	}, [delay, clear]);

	const reset = useCallback(() => {
		clear();
		set();
	}, [clear, set]);

	function isRunning() {
		return timeoutRef.current !== null;
	}

	return { reset, clear, set, isRunning };
}
