import { useCallback, useEffect } from 'react';

type KeyEvents = {[key: string]: () => void};

export function useKeyEvents(events: KeyEvents) {
	const handleKeyDown = useCallback((event: KeyboardEvent) => {
		const eventFunction = events[event.key] || events['any'];
		if (eventFunction) {
			eventFunction();
		}
	}, [events]);

	useEffect(() => {
		document.addEventListener('keydown', handleKeyDown);

		return () => {
			document.removeEventListener('keydown', handleKeyDown);
		};
	}, [handleKeyDown]);
}
