import { useCallback, useMemo, useState } from 'react';
import { AnswerState, Rule, rules } from '@/lib/quiz/rules.ts';
import { countBy } from 'lodash';
import { IncorrectRule } from '@/lib/quiz/rules/IncorrectRule.ts';

export interface OptionState extends Option {
	count: number;
	answerState: AnswerState | null;
}

export function useQuiz(questionData?: Question[]) {
	const [questions] = useState<Question[]>(questionData || []);
	const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
	const [isLocked, setIsLocked] = useState<boolean>(false);

	const [submittedAnswers, setSubmittedAnswers] = useState<string[]>([]);

	const currentQuestion = useMemo(() => questions[currentQuestionIndex], [currentQuestionIndex, questions]);

	const lastAnswer = useMemo(() => submittedAnswers?.[submittedAnswers.length - 1], [submittedAnswers]);

	const validationRule: Rule = useMemo(() => {
		if (!currentQuestion) return new IncorrectRule();

		const { type, params } = currentQuestion.rule;
		const factory = rules[type];

		if (!factory) return new IncorrectRule();

		return factory(params);
	}, [currentQuestion]);

	const currentAnswerState = useMemo(() => {
		return validationRule.validate(submittedAnswers);
	}, [submittedAnswers, validationRule]);

	const submitAnswer = useCallback(
		(answer: string) => {
			if (isLocked || (currentAnswerState === AnswerState.Correct && currentQuestion.template !== 'counter')) return; // Do not allow submitting answers after the question is answered correctly

			setSubmittedAnswers((prev) => [...prev, answer]);
		},
		[currentAnswerState, currentQuestion, isLocked],
	);

	const submitAnswerByIndex = useCallback(
		(index: number) => {
			const answer = currentQuestion.options?.[index]?.id || index.toString();

			if (!answer) {
				throw new Error('Invalid option index');
			}

			submitAnswer(answer);
		},
		[currentQuestion.options, submitAnswer],
	);

	const resetQuestion = useCallback(() => {
		setIsLocked(false);
		setSubmittedAnswers([]);
	}, []);

	const restart = useCallback(() => {
		resetQuestion();
		setCurrentQuestionIndex(0);
	}, [resetQuestion]);

	const answerCounts = useMemo(() => {
		return countBy(submittedAnswers);
	}, [submittedAnswers]);

	const getAnswerCount = useCallback(
		(optionId: string) => {
			return answerCounts[optionId] || 0;
		},
		[answerCounts],
	);

	const nextQuestion = useCallback(() => {
		if (currentQuestionIndex === questions.length - 1) {
			restart();
		} else {
			resetQuestion();
			setCurrentQuestionIndex((prev) => prev + 1);
		}
	}, [currentQuestionIndex, questions.length, resetQuestion, restart]);

	const optionsState = useMemo(() => {
		return (
			currentQuestion?.options?.map((option) => {
				const count = getAnswerCount(option.id);

				const answerState = currentAnswerState;
				const isLastAnswer = submittedAnswers[submittedAnswers.length - 1] === option.id;

				return {
					...option,
					count,
					answerState: isLastAnswer ? answerState : null,
				};
			}) || []
		);
	}, [currentAnswerState, currentQuestion.options, getAnswerCount, submittedAnswers]);

	const timeToAnswer = useMemo(() => {
		return currentQuestion.time || null;
	}, [currentQuestion.time]);

	const lockAnswer = useCallback(() => {
		setIsLocked(true);
	}, []);

	return {
		answerCounts,
		currentAnswerState,
		currentQuestionIndex,
		submitAnswer,
		submitAnswerByIndex,
		submittedAnswers,
		nextQuestion,
		restart,
		questions,
		currentQuestion,
		optionsState,
		lastAnswer,
		timeToAnswer,
		lockAnswer,
	};
}

export type QuizGame = ReturnType<typeof useQuiz>;
