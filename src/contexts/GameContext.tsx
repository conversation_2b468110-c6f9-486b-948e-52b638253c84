import { useTimer } from '@/hooks/useTimer';
import { createContext, PropsWithChildren, useCallback, useContext, useEffect, useState } from 'react';
import { useRootContext } from './RootContext';

interface GameContextProps {
	totalGameTimeLeft: number;
	startGame: () => void;
	startStandby:() => void;
	startIntro: () => void;
	gameState: GameState;
}

const GameContext = createContext<GameContextProps>({} as GameContextProps);

interface GameContextProviderProps {}

export enum GameState {
	Standby,
	Intro,
	Started,
	Outro,
}

export const GameContextProvider = ({ children }: PropsWithChildren<GameContextProviderProps>) => {
	const { gameData } = useRootContext();
	const { start: startGameTimer, stop: stopGameTimer, seconds: totalGameTimeLeft } = useTimer();
	const [gameState, setGameState] = useState<GameState>(GameState.Standby);

	const startStandby = useCallback(() => {
		stopGameTimer();
		setGameState(GameState.Standby);
	}, [stopGameTimer]);

	const startGame = useCallback(() => {
		startGameTimer(gameData.config.timeLimit);
		setGameState(GameState.Started);
	}, [gameData.config.timeLimit, startGameTimer]);

	const startIntro = useCallback(() => {
		setGameState(GameState.Intro)
	}, []);

	useEffect(() => {
		if (gameState === GameState.Started && totalGameTimeLeft === 0) {
			setGameState(GameState.Outro)
			stopGameTimer();
		}
	}, [gameState, stopGameTimer, totalGameTimeLeft]);

	return (
		<GameContext.Provider
			value={{
				startStandby,
				startIntro,
				startGame,
				totalGameTimeLeft,
				gameState,
			}}
		>
			{children}
		</GameContext.Provider>
	);
};

export const useGameContext = () => useContext(GameContext);
