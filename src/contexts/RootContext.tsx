import { createContext, FC, ReactNode, useContext } from 'react';

interface RootContextProps {
	config: ConfigRecord;
	gameData: GameData;
}

export const RootContext = createContext<RootContextProps>({} as RootContextProps);

interface RootContextProviderProps {
	children?: ReactNode;
	config: ConfigRecord;
	gameData: GameData;
}

export const RootContextProvider: FC<RootContextProviderProps> = ({ children, config, gameData }) => {
	return <RootContext.Provider value={{ config, gameData }} children={children} />;
};

export const useRootContext = () => useContext(RootContext);
