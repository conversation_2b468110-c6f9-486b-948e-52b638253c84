import { createContext, PropsWithChildren, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { QuizGame, useQuiz } from '../hooks/useQuiz';
import { useRootContext } from './RootContext';
import { AnswerState } from '@/lib/quiz/rules.ts';
import { useTimer } from '@/hooks/useTimer.ts';
import useTimeout from '@/hooks/useTimeout.ts';
import { useScoreCounter } from '@/hooks/useScoreCounter.ts';

interface QuizContextProps {
	quizGame: QuizGame;
	questionTimeLeft: number;
	totalGameTimeLeft: number;
	questionTimeUp: boolean;
	scoreCounter: ReturnType<typeof useScoreCounter>;
}

const QuizContext = createContext<QuizContextProps>({} as QuizContextProps);

interface QuizContextProviderProps {}

export const QuizContextProvider = ({ children }: PropsWithChildren<QuizContextProviderProps>) => {
	const { gameData } = useRootContext();
	const quizGame: QuizGame = useQuiz(gameData.questions);
	const resultTimeout = useTimeout(quizGame.nextQuestion, 2000);
	const [questionTimeUp, setQuestionTimeUp] = useState(false);
	const scoreCounter = useScoreCounter();
	const answerCountRef = useRef(0);

	const finishAnswering = useCallback(() => {
		quizGame.lockAnswer();
		resultTimeout.reset();
		setQuestionTimeUp(true);
	}, [quizGame, resultTimeout]);

	const { start: startQuestionTimer, stop: stopQuestionTimer, seconds: questionTimeLeft } = useTimer(finishAnswering);
	const { start: startGameTimer, stop: stopGameTimer, seconds: totalGameTimeLeft } = useTimer();

	// TODO: improve score counting event
	useEffect(() => {
		if (quizGame.currentQuestion.rule.type === 'free') {
			const currentAnswersCount = quizGame.submittedAnswers.length;
			if (currentAnswersCount > answerCountRef.current) {
				scoreCounter.addAnswer({ question: quizGame.currentQuestion, answerCounts: quizGame.answerCounts });
			}
			answerCountRef.current = currentAnswersCount;
			return;
		}

		if (quizGame.currentAnswerState === AnswerState.Correct && !resultTimeout.isRunning()) {
			resultTimeout.reset();
			scoreCounter.addAnswer({ question: quizGame.currentQuestion, answerCounts: quizGame.answerCounts });
		}
	}, [
		questionTimeLeft,
		quizGame.answerCounts,
		quizGame.currentAnswerState,
		quizGame.currentQuestion,
		quizGame.submittedAnswers.length,
		resultTimeout,
		scoreCounter,
	]);

	useEffect(() => {
		if (questionTimeLeft > 0) {
			setQuestionTimeUp(false);
		}
	}, [questionTimeLeft]);

	useEffect(() => {
		if (!quizGame.timeToAnswer) return;
		startQuestionTimer(quizGame.timeToAnswer);

		return () => {
			stopQuestionTimer();
		};
	}, [quizGame.timeToAnswer, startQuestionTimer, stopQuestionTimer]);

	useEffect(() => {
		startGameTimer(gameData.config.timeLimit);

		return () => {
			stopGameTimer();
		};
	}, [gameData.config.timeLimit, startGameTimer, stopGameTimer]);

	return (
		<QuizContext.Provider
			value={{
				scoreCounter,
				quizGame,
				questionTimeLeft,
				totalGameTimeLeft,
				questionTimeUp,
			}}
		>
			{children}
		</QuizContext.Provider>
	);
};

export const useQuizContext = () => useContext(QuizContext);
