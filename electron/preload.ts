import { contextBridge } from 'electron';
import { env } from './env';
import { serialApi } from './serial/serialBridge';

export const contextApi = {
	node: () => process.versions.node,
	chrome: () => process.versions.chrome,
	electron: () => process.versions.electron,
	assetDir: !process.env.ELECTRON_DEV ? process.env.ASSET_DIR : undefined,
	isDev: process.env.IS_DEV?.toLowerCase() === 'true',
	env,
	serial: serialApi,
};

contextBridge.exposeInMainWorld('api', contextApi);
