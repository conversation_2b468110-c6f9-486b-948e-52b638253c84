import { DelimiterParser, SerialPort } from "serialport";
import { ipc<PERSON><PERSON>, <PERSON><PERSON>erWindow } from "electron";

const ports = new Map<string, SerialPort>();

export function registerSerialHandlers(win: BrowserWindow) {
	ipcMain.handle("serial:list", async () => {
		return await SerialPort.list();
	});

	ipcMain.handle("serial:open", async (_, path: string, baudRate: number) => {
		if (ports.has(path)) return true;

		const port = new SerialPort({ path, baudRate });
		const parser = new DelimiterParser({ delimiter: "\n", includeDelimiter: false });
		port.pipe(parser);

		parser.on("data", (data: Buffer) => {
			win.webContents.send("serial:data", { path, data: data.toString() });
		});

		ports.set(path, port);
		return true;
	});

	ipcMain.handle("serial:write", async (_, path: string, data: string) => {
		const port = ports.get(path);
		if (port) {
			port.write(data);
			return true;
		}
		return false;
	});

	ipcMain.handle("serial:close", async (_, path: string) => {
		const port = ports.get(path);
		if (port) {
			port.close();
			ports.delete(path);
			return true;
		}
		return false;
	});
}