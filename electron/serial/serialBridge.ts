import { ipc<PERSON><PERSON><PERSON> } from "electron";

export const serialApi = {
	list: () => ipcRenderer.invoke("serial:list"),
	open: (path: string, baudRate: number) => ipcRenderer.invoke("serial:open", path, baudRate),
	write: (path: string, data: string) => ipcRenderer.invoke("serial:write", path, data),
	close: (path: string) => ipcRenderer.invoke("serial:close", path),

	onData: (callback: (msg: { path: string; data: string }) => void) => {
		const listener = (_: Electron.IpcRendererEvent, msg: { path: string; data: string }) => {
			callback(msg);
		};

		ipcRenderer.on("serial:data", listener);

		return () => {
			console.log('Unsubscribing from serial data');
			ipcRenderer.removeListener("serial:data", listener);
		};
	},
}