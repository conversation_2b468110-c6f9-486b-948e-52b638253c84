config:
  timeLimit: 480
  introTimeLimit: 60

questions:
  - id: "1"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "2"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "3"
    rule:
      type: "confirm"
      params:
        id: "D"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "4"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "5"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "6"
    rule:
      type: "confirm"
      params:
        id: "D"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "7"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "8"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "9"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "10"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "100"
    rule:
      type: "free"
    template: "counter"
    time: 15

  - id: "11"
    rule:
      type: "confirm"
      params:
        id: "D"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "12"
    rule:
      type: "confirm"
      params:
        id: "D"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "13"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "14"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "15"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "16"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "17"
    rule:
      type: "confirm"
      params:
        id: "D"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "18"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "19"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "100"
    rule:
      type: "free"
    template: "counter"
    time: 15

  - id: "20"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "21"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "22"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "23"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "24"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "25"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "26"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "27"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "28"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "29"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "100"
    rule:
      type: "free"
    template: "counter"
    time: 15

  - id: "30"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "31"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "32"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "33"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "34"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "35"
    rule:
      type: "confirm"
      params:
        id: "D"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "36"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "37"
    rule:
      type: "confirm"
      params:
        id: "D"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "38"
    rule:
      type: "confirm"
      params:
        id: "D"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "39"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "100"
    rule:
      type: "free"
    template: "counter"
    time: 15

  - id: "40"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "41"
    rule:
      type: "confirm"
      params:
        id: "D"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "42"
    rule:
      type: "confirm"
      params:
        id: "D"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "43"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "44"
    rule:
      type: "confirm"
      params:
        id: "D"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "45"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "46"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "47"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "48"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "49"
    rule:
      type: "confirm"
      params:
        id: "D"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "50"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "51"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "52"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "53"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "54"
    rule:
      type: "confirm"
      params:
        id: "D"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "55"
    rule:
      type: "confirm"
      params:
        id: "C"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "56"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "57"
    rule:
      type: "confirm"
      params:
        id: "A"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "58"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"

  - id: "59"
    rule:
      type: "confirm"
      params:
        id: "B"
        count: 2
    options:
      - id: "A"
      - id: "B"
      - id: "C"
      - id: "D"
