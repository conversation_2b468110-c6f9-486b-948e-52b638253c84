import { defineConfig, IndexHtmlTransformResult, Plugin, ViteDevServer } from 'vite';
import react from '@vitejs/plugin-react';
import serveStatic from 'serve-static';
import 'dotenv/config';
import path from 'path';

import packageJson from './package.json';

const assetPath = process.env.ASSET_DIR;
// const mediaPath = process.env.MEDIA_DIR;

const staticServerPlugin = () => ({
	name: 'serve-static-files',
	configureServer(server: ViteDevServer) {
		server.middlewares.use('/assets', serveStatic(assetPath));
		// server.middlewares.use('/media', serveStatic(mediaPath));
	},
});

const htmlPlugin = (): Plugin => {
	return {
		name: 'html-transform',
		transformIndexHtml(): IndexHtmlTransformResult {
			return [
				{
					tag: 'title',
					children: packageJson.name,
					injectTo: 'head',
				},
			];
		},
	};
};

// https://vitejs.dev/config/
export default defineConfig({
	appType: 'mpa', // avoid fetching an index.html when file is not found
	resolve: {
		alias: {
			'@': path.resolve(__dirname, './src'),
		},
	},
	envPrefix: 'APP_',
	plugins: [
		react(),
		staticServerPlugin(),
		htmlPlugin(),
	],
	base: './',
	build: {
		outDir: 'build',
	},
	define: {
		global: 'window',
	},
});
