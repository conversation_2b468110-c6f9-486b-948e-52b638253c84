type ID = string;

interface IntroSlideOption {
	id: string;
	text: string;
}
interface IntroSlide {
	id: string;
	correctOptions: number[];
	options: IntroSlideOption[];
	rule?: any;
	confirmText?: string;
	correctText?: string;
	onThrow: (optionId?: string) => void;
	onCorrect?: () => void;
}

interface GameType {
	id: string;
	dataFile: string;
	timeLimit: number;
	introTimeLimit: number;
}

interface Option {
	id: ID;
	text: string;
}

type ValidationRuleType = 'free' | 'confirm';

interface ValidationRule {
	type: ValidationRuleType;
	params: never;
}

interface Question {
	id: ID;
	text: string;
	options?: Option[];
	rule: ValidationRule;
	time?: number;
	template?: 'default' | 'counter';
}

interface GameData {
	questions: Question[];
	config: {
		timeLimit: number;
		introTimeLimit: number;
	};
}

interface AnswerSubmittedEventDetails {
	index: number;
}

type AnswerSubmittedEvent = CustomEvent<AnswerSubmittedEventDetails>;
