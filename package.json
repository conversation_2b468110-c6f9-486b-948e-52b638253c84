{"name": "e4-et<PERSON><PERSON><PERSON>us", "private": true, "version": "0.0.0", "main": "build/main.js", "scripts": {"dev": "vite", "dev:host": "vite --host", "build": "vite build", "postbuild": "yarn electron:build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "electron:build": "tsc --project electron", "electron:package": "electron-builder --win", "preelectron:package": "yarn build", "preelectron:dev": "yarn electron:build", "electron:dev": "electron ."}, "dependencies": {"@adonisjs/transmit-client": "^1.0.0", "@platvorm/i18n": "^1.0.0", "@swedbank/components": "^0.1.28", "@swedbank/transmit": "^0.0.1", "@swedbank/utils": "^0.0.1", "dotenv": "^16.3.1", "es-toolkit": "^1.39.10", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "motion": "^12.23.12", "react": "^18.2.0", "react-dom": "^18.2.0", "serialport": "^12.0.0", "styled-components": "^6.1.1"}, "devDependencies": {"@types/js-yaml": "^4.0.9", "@types/lodash": "^4.17.14", "@types/node": "^20.10.5", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/serve-static": "^1.15.5", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "@vitejs/plugin-react": "^4.2.0", "cross-env": "^7.0.3", "electron": "^34.1.1", "electron-builder": "^25.1.8", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.1.0", "serve-static": "^2.1.0", "typescript": "^5.2.2", "vite": "^6.1.0"}, "packageManager": "yarn@1.22.21+sha1.1959a18351b811cdeedbd484a8f86c3cc3bbaf72"}