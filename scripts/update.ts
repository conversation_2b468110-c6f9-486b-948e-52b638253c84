import { writeFile } from 'node:fs/promises';
import { join } from 'node:path';
import 'dotenv/config'

const ASSET_DIR = process.env.ASSET_DIR;
const locales = ['et', 'en'];

const path = '/api/infoscreen/65803b0a949b117a1dddff39';
const base = 'http://localhost:3000';

(async () => {
	await Promise.all(locales.map(async locale => {
		console.log('Fetching ', locale);
		const url = new URL(path, base);
		url.searchParams.set('locale', locale);
		url.searchParams.set('draft', 'false');
		url.searchParams.set('depth', '1');

		const data = await fetch(url);
		const text = await data.text();

		console.log('Writing ', locale);
		await writeFile(join(ASSET_DIR, `data.${locale}.json`), text);
	}));
})();
